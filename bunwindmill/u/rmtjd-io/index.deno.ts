import { createPublicClient, http, formatEther } from "viem";
import { bsc } from "viem/chains";

const contractAddress = "******************************************";
const walletToMonitor = "******************************************";
const alertThreshold = 1000000; // 1M tokens

// 创建 BSC 客户端
const client = createPublicClient({
  chain: bsc,
  transport: http(),
});

const abi = [
  {
    inputs: [{ name: "_owner", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "balance", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
] as const;

async function getBalance() {
  const balance = await client.readContract({
    address: contractAddress,
    abi: abi,
    functionName: "balanceOf",
    args: [walletToMonitor],
  });

  const balanceInTokens = Number(formatEther(balance));
  return balanceInTokens;
}
const balance = await getBalance();
console.log(balance);